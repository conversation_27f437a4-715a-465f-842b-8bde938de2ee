import { TRPCError } from "@trpc/server";
import { and, eq, gte, type InferInsertModel, lte, or } from "drizzle-orm";
import { z } from "zod";
import { db } from "@/db";
import { bid, job, schedule } from "@/db/schema";
import { protectedProcedure, router } from "../trpc";
import {
  checkJobOwnership,
  checkOrganizationMembership,
  requireAuth,
} from "../utils/permissions";

export const schedulesRouter = router({
  proposeSchedule: protectedProcedure
    .input(
      z.object({
        jobId: z.string(),
        proposedStartDate: z.date(),
        proposedEndDate: z.date(),
        notes: z.string().optional(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      // Get the job
      const jobData = await db.query.job.findFirst({
        where: eq(job.id, input.jobId),
        with: {
          property: true,
          bids: {
            where: eq(bid.status, "ACCEPTED"),
            with: {
              organization: true,
            },
          },
        },
      });

      if (!jobData) {
        throw new TRPCError({ code: "NOT_FOUND", message: "Job not found" });
      }

      // Check permissions
      const isPropertyOwner = await checkJobOwnership(ctx.userId, input.jobId);
      const acceptedBid = jobData.bids.find((bid) => bid.status === "ACCEPTED");

      let isMember = false;
      if (acceptedBid) {
        isMember = await checkOrganizationMembership(
          ctx.userId,
          acceptedBid.organization.id,
        );
      }

      requireAuth(
        isPropertyOwner || isMember,
        "You don't have permission to propose a schedule for this job",
      );

      // Check for overlapping schedules
      const overlappingSchedules = await db.query.schedule.findMany({
        where: and(
          eq(schedule.jobId, input.jobId),
          or(
            and(
              gte(schedule.proposedStartDate, input.proposedStartDate),
              lte(schedule.proposedStartDate, input.proposedEndDate),
            ),
            and(
              gte(schedule.proposedEndDate, input.proposedStartDate),
              lte(schedule.proposedEndDate, input.proposedEndDate),
            ),
          ),
        ),
      });

      if (overlappingSchedules.length > 0) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "The proposed schedule overlaps with existing schedules",
        });
      }

      const scheduleData: InferInsertModel<typeof schedule> = {
        jobId: input.jobId,
        proposedStartDate: input.proposedStartDate,
        proposedEndDate: input.proposedEndDate,
        notes: input.notes,
        status: "PROPOSED",
        proposedById: ctx.userId,
        proposedByRole: ctx.role || "contractor",
      };

      // Create the schedule
      const [newSchedule] = await db
        .insert(schedule)
        .values(scheduleData)
        .returning();

      return newSchedule;
    }),

  confirmSchedule: protectedProcedure
    .input(
      z.object({
        scheduleId: z.string(),
        confirmedStartDate: z.date(),
        confirmedEndDate: z.date(),
      }),
    )
    .mutation(async ({ input }) => {
      return db
        .update(schedule)
        .set({
          confirmedStartDate: input.confirmedStartDate,
          confirmedEndDate: input.confirmedEndDate,
          status: "CONFIRMED",
        })
        .where(eq(schedule.id, input.scheduleId));
    }),

  markComplete: protectedProcedure
    .input(z.object({ scheduleId: z.string() }))
    .mutation(async ({ input }) => {
      const scheduleData = await db.query.schedule.findFirst({
        where: eq(schedule.id, input.scheduleId),
        with: { job: true },
      });

      if (!scheduleData) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Schedule not found",
        });
      }

      // Mark schedule as complete
      const updatedSchedule = await db
        .update(schedule)
        .set({
          completed: true,
          completedAt: new Date(),
        })
        .where(eq(schedule.id, input.scheduleId));

      // Also mark the job as completed if it's a one-time job
      if (!scheduleData.job.isRecurring) {
        await db
          .update(job)
          .set({
            status: "COMPLETED",
            completedAt: new Date(),
          })
          .where(eq(job.id, scheduleData.job.id));
      }

      return updatedSchedule;
    }),

  getJobSchedule: protectedProcedure
    .input(z.object({ jobId: z.string() }))
    .query(async ({ input }) => {
      return db.query.schedule.findFirst({
        where: eq(schedule.jobId, input.jobId),
      });
    }),
});
