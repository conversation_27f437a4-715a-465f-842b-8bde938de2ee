import { TRPCError } from "@trpc/server";
import { and, eq } from "drizzle-orm";
import { z } from "zod";
import { db } from "@/db";
import { address, property } from "@/db/schema";
import { geocodeAddress } from "@/lib/geocoding";
import { protectedProcedure, router } from "../trpc";
import { createAddress } from "../utils/addresses";

export const propertiesRouter = router({
  list: protectedProcedure.query(async ({ ctx }) => {
    const properties = await db
      .select()
      .from(property)
      .innerJoin(address, eq(property.addressId, address.id))
      .where(eq(property.userId, ctx.userId));

    return properties.map((property) => ({
      ...property.property,
      address: property.address,
    }));
  }),
  create: protectedProcedure
    .input(
      z.object({
        name: z.string(),
        imageUrl: z.string(),
        address: z.object({
          street: z.string(),
          city: z.string(),
          state: z.string(),
          zip: z.string(),
        }),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      if (!ctx.userId) {
        throw new TRPCError({ code: "UNAUTHORIZED" });
      }

      // Geocode the address
      const location = await geocodeAddress(input.address);

      const address = await createAddress(
        {
          street: input.address.street,
          city: input.address.city,
          state: input.address.state,
          zip: input.address.zip,
          location: location,
        },
        db,
      );

      if (!address) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create address",
        });
      }

      // Then create the property with the address
      const [newProperty] = await db
        .insert(property)
        .values({
          name: input.name,
          imageUrl: input.imageUrl,
          userId: ctx.userId,
          addressId: address.id,
        })
        .returning();

      return newProperty;
    }),
  update: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        name: z.string(),
        address: z.object({
          street: z.string(),
          city: z.string(),
          state: z.string(),
          zip: z.string(),
        }),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      if (!ctx.userId) {
        throw new TRPCError({ code: "UNAUTHORIZED" });
      }

      // Get the property to update
      const p = await db.query.property.findFirst({
        where: eq(property.id, input.id),
        with: { address: true },
      });

      if (!p) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Property not found",
        });
      }

      // Geocode the address
      const location = await geocodeAddress(input.address);

      // Update the address
      await db
        .update(address)
        .set({
          street: input.address.street,
          city: input.address.city,
          state: input.address.state,
          zip: input.address.zip,
          location: location,
        })
        .where(eq(address.id, p.address.id));

      // Update the property
      const [updatedProperty] = await db
        .update(property)
        .set({
          name: input.name,
        })
        .where(eq(property.id, input.id))
        .returning();

      return updatedProperty;
    }),
  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input }) => {
      return await db.delete(property).where(eq(property.id, input.id));
    }),
  one: protectedProcedure
    .input(
      z.object({
        id: z.string(),
      }),
    )
    .query(async ({ input, ctx }) => {
      if (!ctx.userId) {
        throw new TRPCError({ code: "UNAUTHORIZED" });
      }

      const [result] = await db
        .select()
        .from(property)
        .innerJoin(address, eq(property.addressId, address.id))
        .where(and(eq(property.id, input.id), eq(property.userId, ctx.userId)));

      if (!result) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Property not found",
        });
      }

      return {
        ...result.property,
        address: result.address,
      };
    }),
});
