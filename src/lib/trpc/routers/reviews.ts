import { TRPCError } from "@trpc/server";
import { and, desc, eq, inArray } from "drizzle-orm";
import { z } from "zod";
import { db } from "@/db";
import { bid, job, review } from "@/db/schema";
import { protectedProcedure, router } from "../trpc";

export const reviewsRouter = router({
  create: protectedProcedure
    .input(
      z.object({
        jobId: z.string(),
        rating: z.number().min(1).max(5),
        comment: z.string(),
        reviewType: z.enum(["HOMEOWNER_REVIEW", "CONTRACTOR_REVIEW"]),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      // Get the job with related data
      const j = await db.query.job.findFirst({
        where: eq(job.id, input.jobId),
        with: {
          property: {
            with: {
              user: true,
            },
          },
          bids: {
            where: eq(bid.status, "ACCEPTED"),
            with: {
              organization: {
                with: {
                  memberships: true,
                },
              },
            },
          },
        },
      });

      if (!j) {
        throw new TRPCError({ code: "NOT_FOUND", message: "Job not found" });
      }

      // Check authorization based on review type
      if (input.reviewType === "HOMEOWNER_REVIEW") {
        // Contractor reviewing homeowner
        const acceptedBid = j.bids.find((bid) => bid.status === "ACCEPTED");
        if (!acceptedBid) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "No accepted bid found for this job",
          });
        }

        const isMember = acceptedBid.organization.memberships.some(
          (m) => m.userId === ctx.userId,
        );

        if (!isMember) {
          throw new TRPCError({ code: "UNAUTHORIZED" });
        }
      } else {
        // Homeowner reviewing contractor
        const isPropertyOwner = j.property.user.id === ctx.userId;
        if (!isPropertyOwner) {
          throw new TRPCError({ code: "UNAUTHORIZED" });
        }
      }

      // Create the review
      const [result] = await db
        .insert(review)
        .values({
          jobId: input.jobId,
          rating: input.rating,
          comment: input.comment,
          reviewType: input.reviewType,
          reviewerId: ctx.userId,
        })
        .returning();

      return result;
    }),

  listForJob: protectedProcedure
    .input(z.object({ jobId: z.string() }))
    .query(async ({ input, ctx }) => {
      const j = await db.query.job.findFirst({
        where: eq(job.id, input.jobId),
        with: {
          property: {
            with: {
              user: true,
            },
          },
          bids: {
            where: eq(bid.status, "ACCEPTED"),
            with: {
              organization: {
                with: {
                  memberships: true,
                },
              },
            },
          },
        },
      });

      if (!j) {
        throw new TRPCError({ code: "NOT_FOUND", message: "Job not found" });
      }

      // Check if user has permission to view this job
      const isPropertyOwner = j.property.user.id === ctx.userId;
      const acceptedBid = j.bids.find((bid) => bid.status === "ACCEPTED");
      const isMember = acceptedBid?.organization.memberships.some(
        (m) => m.userId === ctx.userId,
      );

      if (!isPropertyOwner && !isMember) {
        throw new TRPCError({ code: "UNAUTHORIZED" });
      }

      return db.query.review.findMany({
        where: eq(review.jobId, input.jobId),
        orderBy: [desc(review.createdAt)],
      });
    }),

  listForOrganization: protectedProcedure
    .input(z.object({ organizationId: z.string() }))
    .query(async ({ input }) => {
      // Get all jobs where this organization had an accepted bid
      const jobList = await db.query.job.findMany({
        where: and(
          eq(bid.organizationId, input.organizationId),
          eq(bid.status, "ACCEPTED"),
          eq(job.status, "COMPLETED"),
        ),
        with: {
          bids: {
            where: and(
              eq(bid.organizationId, input.organizationId),
              eq(bid.status, "ACCEPTED"),
            ),
          },
        },
      });

      const jobIds = jobList.map((job) => job.id);

      // Get all contractor reviews for these jobs
      const reviewList = await db.query.review.findMany({
        where: and(
          inArray(review.jobId, jobIds),
          eq(review.reviewType, "CONTRACTOR_REVIEW"),
        ),
        with: {
          job: true,
        },
        orderBy: [desc(review.createdAt)],
      });

      return reviewList;
    }),

  listForOrganizations: protectedProcedure
    .input(z.object({ organizationIds: z.array(z.string()) }))
    .query(async ({ input }) => {
      if (input.organizationIds.length === 0) {
        return [];
      }

      // Get all jobs where these organizations had accepted bids
      const jobList = await db.query.job.findMany({
        where: and(
          eq(job.status, "COMPLETED"),
          eq(bid.status, "ACCEPTED"),
          inArray(bid.organizationId, input.organizationIds),
        ),
        with: {
          bids: {
            where: and(
              eq(bid.status, "ACCEPTED"),
              inArray(bid.organizationId, input.organizationIds),
            ),
          },
        },
      });

      const jobIds = jobList.map((job) => job.id);

      // Get all contractor reviews for these jobs
      const reviewList = await db.query.review.findMany({
        where: and(
          inArray(review.jobId, jobIds),
          eq(review.reviewType, "CONTRACTOR_REVIEW"),
        ),
        with: {
          job: {
            with: {
              bids: {
                where: and(
                  eq(bid.status, "ACCEPTED"),
                  inArray(bid.organizationId, input.organizationIds),
                ),
                columns: {
                  organizationId: true,
                  status: true,
                },
              },
            },
          },
        },
        orderBy: [desc(review.createdAt)],
      });

      return reviewList;
    }),
});
