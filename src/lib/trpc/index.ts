import { createContext } from "./context";
import { accountsRouter } from "./routers/accounts";
import { adminRouter } from "./routers/admin";
import { bidsRouter } from "./routers/bids";
import { contractorRouter } from "./routers/contractor";
import { jobsRouter } from "./routers/jobs";
import { messagesRouter } from "./routers/messages";
import { propertiesRouter } from "./routers/properties";
import { reviewsRouter } from "./routers/reviews";
import { schedulesRouter } from "./routers/schedules";
import { templatesRouter } from "./routers/templates";
import { tradesRouter } from "./routers/trades";
import { usersRouter } from "./routers/users";
import { router } from "./trpc";

export const appRouter = router({
  admin: adminRouter,
  bids: bidsRouter,
  accounts: accountsRouter,
  trades: tradesRouter,
  contractor: contractorRouter,
  jobs: jobsRouter,
  properties: propertiesRouter,
  schedules: schedulesRouter,
  messages: messagesRouter,
  templates: templatesRouter,
  reviews: reviewsRouter,
  users: usersRouter,
});

export type AppRouter = typeof appRouter;

export const caller = appRouter.createCaller(createContext);
