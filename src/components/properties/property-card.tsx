import { Building2, MapPin } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";
import { JobWizard } from "@/components/job/job-wizard";
import type { Property } from "@/db/schema";
import { cn } from "@/lib/utils";
import { Badge } from "../ui/badge";
import { Button, buttonVariants } from "../ui/button";
import { Card, CardContent, CardFooter, CardTitle } from "../ui/card";

export function PropertyCard({ property }: Readonly<{ property: Property }>) {
  const [wizardOpen, setWizardOpen] = useState(false);
  return (
    <>
      <Card className="group relative flex h-full max-w-80 flex-col overflow-hidden transition-all duration-300 hover:shadow-md">
        <div className="-mt-6 relative h-48 w-full overflow-hidden ">
          <Image
            src={property.imageUrl || ""}
            alt={property.name}
            width={400}
            height={300}
            className="h-full w-full object-cover transition-transform duration-500 group-hover:scale-105"
            priority={false}
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
        </div>

        <CardContent className="flex h-full flex-1 flex-col p-4">
          <CardTitle className="line-clamp-1 text-xl">
            {property.name}
          </CardTitle>

          {property.address && (
            <div className="mt-2 flex items-start gap-1.5 text-muted-foreground text-sm">
              <MapPin className="mt-0.5 h-4 w-4 flex-shrink-0" />
              <span className="line-clamp-2">
                {property.address.street}, {property.address.city},{" "}
                {property.address.state} {property.address.zip}
              </span>
            </div>
          )}

          <div className="mt-3 flex flex-wrap gap-2">
            <Badge
              variant="outline"
              className="bg-orange-50 text-orange-700 hover:bg-orange-100"
            >
              <Building2 className="mr-1 h-3 w-3" />
              Property
            </Badge>
          </div>
        </CardContent>

        <CardFooter className="-mb-6 flex justify-between border-t bg-muted/30 p-4">
          <div className="flex w-full items-center gap-2">
            <Link
              className={cn(
                buttonVariants({ variant: "tc_blue", size: "sm" }),
                "flex-1",
              )}
              href={`/properties/${property.id}`}
            >
              View Details
            </Link>

            <Link
              className={cn(
                buttonVariants({ variant: "outline", size: "sm" }),
                "flex-shrink-0",
              )}
              href={`/properties/${property.id}/edit`}
            >
              Edit
            </Link>
            <Button
              variant="tc_orange"
              size="sm"
              onClick={() => setWizardOpen(true)}
            >
              New Project
            </Button>
          </div>
        </CardFooter>
      </Card>
      <JobWizard
        open={wizardOpen}
        onOpenChange={setWizardOpen}
        propertyId={property.id}
      />
    </>
  );
}
