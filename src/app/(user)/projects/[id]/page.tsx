"use client";

import { useMutation, useQuery } from "@tanstack/react-query";
import { format, isPast } from "date-fns";
import {
  CalendarIcon,
  ChevronDown,
  ChevronDownIcon,
  ClockIcon,
  DollarSignIcon,
  HandIcon,
  MapPinIcon,
  PencilIcon,
  SendIcon,
  Star,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";
import { BidCard } from "@/components/bid/bid-card";
import { PusherChat } from "@/components/chat";
import { useOrganization } from "@/components/contexts/organization-context";
import { CrewList } from "@/components/contractor/crew-list";
import { ImageViewerDialog } from "@/components/job/image-viewer-dialog";
import { PageLayout } from "@/components/page-layout";
import { CompleteScheduleButton } from "@/components/schedule/complete-schedule-button";
import { HomeownerBidFlowTour } from "@/components/tours/homeowner-bid-flow-tour";
import { useTRPC } from "@/components/trpc/client";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ButtonGroup } from "@/components/ui/button-group";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useSession } from "@/lib/auth-client";
import { getStatusVariant, JOB_STATUS_VARIANTS } from "@/lib/utils";

export default function JobDetailPage() {
  const { id } = useParams<{ id: string }>();
  const router = useRouter();
  const trpc = useTRPC();
  const [isPublishing, setIsPublishing] = useState(false);
  const { organization } = useOrganization();
  const { data: session } = useSession();

  const { data: job } = useQuery(
    trpc.jobs.getById.queryOptions({
      id,
      organizationId: organization?.id || "",
    }),
  );

  const publishJob = useMutation(
    trpc.jobs.publish.mutationOptions({
      onSuccess: () => {
        toast.success("Project published successfully");
        router.refresh();
      },
      onError: (error) => {
        toast.error(`Error publishing project: ${error.message}`);
      },
      onSettled: () => {
        setIsPublishing(false);
      },
    }),
  );

  const cancelJob = useMutation(
    trpc.jobs.cancel.mutationOptions({
      onSuccess: () => {
        toast.success("Project cancelled successfully");
        router.push("/dashboard");
      },
      onError: (error) => {
        toast.error(`Error cancelling project: ${error.message}`);
      },
    }),
  );

  if (!job) {
    return null;
  }

  const isProfessional = session?.user?.role === "contractor";
  const isHomeowner = session?.user?.role === "homeowner";
  const isDraft = job.status === "DRAFT";
  const isPublished = job.status === "PUBLISHED";
  const isBidDeadlinePassed = isPast(job.deadline);
  const isQuickHire = job.jobType === "QUICK_HIRE";

  const handlePublish = () => {
    setIsPublishing(true);
    publishJob.mutate({ id: job.id });
  };

  const handleCancel = () => {
    if (window.confirm("Are you sure you want to cancel this project?")) {
      cancelJob.mutate({ id: job.id });
    }
  };

  const acceptedBid = job.bids?.find((bid) => bid.status === "ACCEPTED");

  const actions = (
    <div className="flex gap-2">
      {!isProfessional && isDraft && (
        <Button
          variant="tc_blue"
          onClick={handlePublish}
          disabled={isPublishing}
        >
          <SendIcon className="mr-2 h-4 w-4" />
          {isPublishing ? "Publishing..." : "Publish Project"}
        </Button>
      )}

      {isProfessional && isPublished && !isBidDeadlinePassed && (
        <Button variant="tc_blue" asChild>
          <Link href={`/jobs/${job.id}/bid`}>
            <HandIcon className="mr-2 h-4 w-4" />
            Bid Now
          </Link>
        </Button>
      )}

      {!isProfessional &&
        !(job.status === "AWARDED" || job.status === "COMPLETED") && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="flex items-center gap-1">
                <PencilIcon className="h-4 w-4" />
                Actions
                <ChevronDownIcon className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem asChild>
                <Link href={`/projects/${job.id}/edit`}>Edit Project</Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href={`/projects/${job.id}/tasks/new`}>Add Task</Link>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}

      {isHomeowner && job.status === "AWARDED" && (
        <>
          {!isQuickHire && (
            <Button
              className={
                job.homeownerCompleted
                  ? "bg-gray-500 hover:bg-gray-600"
                  : "bg-green-600 hover:bg-green-700"
              }
              asChild
            >
              <Link href={`/projects/${job.id}/complete`}>
                {job.homeownerCompleted
                  ? "Completion Pending"
                  : "Mark Complete"}
              </Link>
            </Button>
          )}

          {isQuickHire && !job.schedules && (
            <Button className="bg-blue-600 hover:bg-blue-700" asChild>
              <Link href={`/projects/${job.id}/schedule`}>
                <CalendarIcon className="mr-2 h-4 w-4" />
                Schedule Job
              </Link>
            </Button>
          )}

          {isQuickHire &&
            job.schedules &&
            job.schedules[0]?.status === "PROPOSED" &&
            job.schedules[0].proposedByRole !== "HOMEOWNER" && (
              <Button className="bg-blue-600 hover:bg-blue-700" asChild>
                <Link href={`/projects/${job.id}/schedule`}>
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  Confirm Schedule
                </Link>
              </Button>
            )}

          {isQuickHire &&
            job.schedules &&
            job.schedules[0]?.status === "CONFIRMED" &&
            !job.schedules[0].completed && (
              <CompleteScheduleButton
                scheduleId={job.schedules[0].id}
                jobId={job.id}
                isRecurring={job.isRecurring}
                onComplete={() => router.refresh()}
              />
            )}
        </>
      )}

      {isProfessional && job.status === "AWARDED" && (
        <>
          {!isQuickHire && (
            <Button
              className={
                job.contractorCompleted
                  ? "bg-gray-500 hover:bg-gray-600"
                  : "bg-green-600 hover:bg-green-700"
              }
              asChild
            >
              <Link href={`/projects/${job.id}/complete`}>
                {job.contractorCompleted
                  ? "Completion Pending"
                  : "Mark Complete"}
              </Link>
            </Button>
          )}

          {isQuickHire && !job.schedules && (
            <Button className="bg-blue-600 hover:bg-blue-700" asChild>
              <Link href={`/projects/${job.id}/schedule`}>
                <CalendarIcon className="mr-2 h-4 w-4" />
                Schedule Job
              </Link>
            </Button>
          )}

          {isQuickHire &&
            job.schedules &&
            job.schedules[0]?.status === "PROPOSED" &&
            job.schedules[0].proposedByRole !== "PROFESSIONAL" && (
              <Button className="bg-blue-600 hover:bg-blue-700" asChild>
                <Link href={`/projects/${job.id}/schedule`}>
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  Confirm Schedule
                </Link>
              </Button>
            )}

          {isQuickHire &&
            job.schedules &&
            job.schedules[0]?.status === "CONFIRMED" &&
            !job.schedules[0].completed && (
              <CompleteScheduleButton
                scheduleId={job.schedules[0].id}
                jobId={job.id}
                isRecurring={job.isRecurring}
                onComplete={() => router.refresh()}
              />
            )}
        </>
      )}

      {isHomeowner && job.status === "COMPLETED" && (
        <Button className="bg-blue-600 hover:bg-blue-700" asChild>
          <Link href={`/projects/${job.id}/review`}>
            <Star className="mr-2 h-4 w-4" />
            Leave Review
          </Link>
        </Button>
      )}

      {isProfessional && job.status === "COMPLETED" && (
        <Button className="bg-blue-600 hover:bg-blue-700" asChild>
          <Link href={`/projects/${job.id}/review`}>
            <Star className="mr-2 h-4 w-4" />
            Leave Review
          </Link>
        </Button>
      )}

      <Button className="bg-tradecrews-orange hover:bg-orange-700" asChild>
        <Link href="/dashboard">Back to Dashboard</Link>
      </Button>
    </div>
  );

  return (
    <PageLayout title={`Project: ${job.name}`} actions={actions}>
      {isHomeowner &&
        job.status === "PUBLISHED" &&
        job.bids &&
        job.bids.length > 0 && <HomeownerBidFlowTour />}
      <div className="p-8">
        <div className="grid gap-6 md:grid-cols-3">
          <div className="md:col-span-2">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Project Details</CardTitle>
                  <Badge
                    variant={getStatusVariant(job.status, JOB_STATUS_VARIANTS)}
                  >
                    {job.status}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h3 className="font-medium">Property</h3>
                    <p>{job.property.name}</p>
                    <div className="flex items-center gap-2">
                      <MapPinIcon className="size-4 text-tradecrews-orange" />
                      {job.property.address && (
                        <p className="text-muted-foreground">
                          {job.property.address.street},{" "}
                          {job.property.address.city},{" "}
                          {job.property.address.state}{" "}
                          {job.property.address.zip}
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-4">
                    <div className="flex items-center gap-2">
                      <CalendarIcon className="h-4 w-4 text-tradecrews-orange" />
                      <div>
                        <p className="text-muted-foreground text-sm">
                          Start Date
                        </p>
                        <p>{format(job.startsAt, "PPP")}</p>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <ClockIcon className="h-4 w-4 text-tradecrews-orange" />
                      <div>
                        <p className="text-muted-foreground text-sm">
                          Bid Deadline
                        </p>
                        <p>
                          {format(job.deadline, "PPP")}
                          {isBidDeadlinePassed && (
                            <span className="ml-2 font-medium text-red-500 text-xs">
                              (Passed)
                            </span>
                          )}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <DollarSignIcon className="h-4 w-4 text-tradecrews-orange" />
                      <div>
                        <p className="text-muted-foreground text-sm">Budget</p>
                        <p>${job.budget}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {isQuickHire && job.schedules && job.schedules[0] && (
              <Card className="mt-6">
                <CardHeader>
                  <CardTitle>Schedule Details</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex flex-wrap gap-4">
                      <div className="flex items-center gap-2">
                        <CalendarIcon className="h-4 w-4 text-tradecrews-orange" />
                        <div>
                          <p className="text-muted-foreground text-sm">
                            {job.schedules[0].status === "CONFIRMED"
                              ? "Confirmed Date"
                              : "Proposed Date"}
                          </p>
                          <p>
                            {format(
                              job.schedules[0].status === "CONFIRMED"
                                ? (job.schedules[0].confirmedStartDate as Date)
                                : (job.schedules[0].proposedStartDate as Date),
                              "PPP",
                            )}
                            {job.schedules[0].status === "CONFIRMED" &&
                            job.schedules[0].confirmedEndDate &&
                            job.schedules[0].confirmedStartDate?.toDateString() !==
                              job.schedules[0].confirmedEndDate.toDateString()
                              ? ` - ${format(
                                  job.schedules[0].confirmedEndDate,
                                  "PPP",
                                )}`
                              : ""}
                            {job.schedules[0].status === "PROPOSED" &&
                            job.schedules[0].proposedEndDate &&
                            job.schedules[0].proposedStartDate.toDateString() !==
                              job.schedules[0].proposedEndDate.toDateString()
                              ? ` - ${format(
                                  job.schedules[0].proposedEndDate,
                                  "PPP",
                                )}`
                              : ""}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <ClockIcon className="h-4 w-4 text-tradecrews-orange" />
                        <div>
                          <p className="text-muted-foreground text-sm">
                            {job.schedules[0].status === "CONFIRMED"
                              ? "Confirmed Time"
                              : "Proposed Time"}
                          </p>
                          <p>
                            {job.schedules[0].status === "CONFIRMED" &&
                            job.schedules[0].confirmedStartDate
                              ? `${format(
                                  job.schedules[0].confirmedStartDate,
                                  "h:mm a",
                                )}`
                              : ""}
                            {job.schedules[0].status === "PROPOSED" &&
                            job.schedules[0].proposedStartDate
                              ? `${format(
                                  job.schedules[0].proposedStartDate,
                                  "h:mm a",
                                )}`
                              : ""}
                            {job.schedules[0].status === "CONFIRMED" &&
                            job.schedules[0].confirmedEndDate
                              ? ` - ${format(
                                  job.schedules[0].confirmedEndDate,
                                  "h:mm a",
                                )}`
                              : ""}
                            {job.schedules[0].status === "PROPOSED" &&
                            job.schedules[0].proposedEndDate
                              ? ` - ${format(
                                  job.schedules[0].proposedEndDate,
                                  "h:mm a",
                                )}`
                              : ""}
                          </p>
                        </div>
                      </div>
                    </div>

                    {job.schedules[0].notes && (
                      <div>
                        <h3 className="font-medium">Notes</h3>
                        <p className="text-muted-foreground">
                          {job.schedules[0].notes}
                        </p>
                      </div>
                    )}

                    {job.schedules[0].completed && (
                      <div className="rounded-md border border-green-200 bg-green-50 p-3">
                        <p className="font-medium text-green-800">
                          Completed on{" "}
                          {format(job.schedules[0].completedAt as Date, "PPP")}
                        </p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {job.images && job.images.length > 0 && (
              <Card className="mt-6">
                <CardHeader>
                  <CardTitle>Project Images</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-3">
                    {job.images.map((image, index) => (
                      <div
                        key={image.id}
                        className="overflow-hidden rounded-md border"
                      >
                        <ImageViewerDialog
                          images={job.images.map((img) => ({
                            url: img.url,
                            description: img.description,
                          }))}
                          initialIndex={index}
                          title={`${job.name} - Project Images`}
                        >
                          <div className="relative aspect-video cursor-pointer">
                            <Image
                              src={image.url}
                              alt={
                                image.description ||
                                `Project image ${index + 1}`
                              }
                              fill
                              className="object-cover"
                            />
                          </div>
                        </ImageViewerDialog>
                        {image.description && (
                          <div className="p-3">
                            <p className="text-sm">{image.description}</p>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            <Card className="mt-6">
              <CardHeader>
                <CardTitle>Tasks</CardTitle>
              </CardHeader>
              <CardContent>
                {job.tasks.length === 0 ? (
                  <p className="text-muted-foreground">
                    No tasks added to this project.
                  </p>
                ) : (
                  <div className="space-y-4">
                    {job.tasks.map((task, index) => (
                      <div key={task.id} className="rounded-md border p-4">
                        <h4 className="font-medium">
                          Task {index + 1}: {task.name}
                        </h4>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          <div>
            {isQuickHire ? (
              <Card>
                <CardHeader>
                  <CardTitle>Messages</CardTitle>
                </CardHeader>
                <CardContent>
                  <PusherChat
                    jobId={job.id}
                    userId={session?.user?.id as string}
                  />
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardHeader>
                  <CardTitle>Bids</CardTitle>
                </CardHeader>
                <CardContent>
                  {job.bids && job.bids.length > 0 ? (
                    <div className="space-y-4">
                      {job.bids.map((bid) => (
                        <BidCard
                          key={bid.id}
                          bidId={bid.id}
                          showAcceptButton={
                            isHomeowner && job.status === "PUBLISHED"
                          }
                        />
                      ))}
                    </div>
                  ) : (
                    <div className="rounded-md bg-muted/50 p-4 text-center text-muted-foreground">
                      <p>No bids have been submitted for this job yet.</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {acceptedBid && (
              <div className="mt-6">
                <h3 className="mb-2 font-medium">Contractor's Crew</h3>
                <CrewList organizationId={acceptedBid.organization.id} />
              </div>
            )}

            <div className="mt-6 space-y-4">
              {isDraft && !isProfessional && (
                <ButtonGroup className="w-full">
                  <Button
                    className="flex-1"
                    variant="tc_blue"
                    onClick={handlePublish}
                    disabled={isPublishing}
                  >
                    <SendIcon className="mr-2 h-4 w-4" />
                    {isPublishing ? "Publishing..." : "Publish Project"}
                  </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="tc_blue" size="icon">
                        <ChevronDown />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-56">
                      <DropdownMenuItem asChild>
                        <Button
                          variant="destructive"
                          className="w-full"
                          onClick={handleCancel}
                        >
                          Cancel Project
                        </Button>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </ButtonGroup>
              )}

              {!isProfessional &&
                !(job.status === "AWARDED" || job.status === "COMPLETED") && (
                  <Button className="w-full" variant="tc_orange" asChild>
                    <Link href={`/projects/${job.id}/edit`}>Edit Project</Link>
                  </Button>
                )}

              <Button variant="outline" className="w-full" asChild>
                <Link href="/dashboard">Back to Dashboard</Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </PageLayout>
  );
}
