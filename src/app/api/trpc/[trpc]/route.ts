import { fetchRe<PERSON><PERSON>and<PERSON> } from "@trpc/server/adapters/fetch";
import posthog from "posthog-js";
import { appRouter } from "@/lib/trpc";
import { createContext } from "@/lib/trpc/context";

function handler(req: Request) {
  return fetchRequestHandler({
    endpoint: "/api/trpc",
    req,
    router: appRouter,
    createContext,
    onError(opts) {
      console.error("TRPC Error:", opts.error);
      posthog.captureException(opts.error, { ...opts });
    },
  });
}

export { handler as GET, handler as POST };
